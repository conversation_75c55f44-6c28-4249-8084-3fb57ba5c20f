import React, {lazy, Suspense, useEffect, useState} from "react";
import {ConfigProvider} from "antd";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import {useDispatch, useSelector} from "react-redux";
import './i18n';
import frFR from "antd/locale/fr_FR";
import arEG from "antd/locale/ar_EG";
//import enUS from "antd/locale/en_US";


/*|--------------------------------------------------------------------------
| i18n translated documents
|-------------------------------------------------------------------------- */
import {useTranslation} from "react-i18next";
import {checkAuthStatus} from "./features/auth/authSlice.ts";
import {AppDispatch} from "./features/store.ts";
import useRefreshToken from "./hooks/useRefreshToken.tsx";
import { useSingleTabGuard } from "./hooks/useSingleTabGuard.ts";
import { t } from "i18next";

/*|--------------------------------------------------------------------------
| Layouts
|-------------------------------------------------------------------------- */
const AuthLayout = lazy(() => import("./layouts/AuthLayout"));
const GuestLayout = lazy(() => import("./layouts/GuestLayout"));

/*|--------------------------------------------------------------------------
| auth pages
|-------------------------------------------------------------------------- */
const AccessDenied = lazy(() => import("./pages/auth/AccessDenied"));
const Login = lazy(() => import("./pages/auth/Login"));
const NotFound = lazy(() => import("./pages/auth/NotFound"));
const Loading = lazy(() => import("./components/Loading"));
const ResetPassword = lazy(() => import("./pages/auth/ResetPassword"));

/*|--------------------------------------------------------------------------
| admin pages - profiles
|-------------------------------------------------------------------------- */
const ManageProfile = lazy(() => import("./pages/admin/users/ManageProfile"));

/*|--------------------------------------------------------------------------
| admin pages - configs
|-------------------------------------------------------------------------- */
const Dashboard = lazy(() => import("./pages/admin/Dashboard"));
const ManageAdmins = lazy(() => import("./pages/admin/users/ManageAdmins"));

const ManageClients = lazy(() => import("./pages/admin/users/ManageClients"));
const ManageGovernorates = lazy(() => import("./pages/admin/configs/ManageGovernorates"));
const ManageDelegations = lazy(() => import("./pages/admin/configs/ManageDelegations"));
const ManageStations = lazy(() => import("./pages/admin/configs/ManageStations"));
const ManageEstablishments = lazy(() => import("./pages/admin/configs/ManageEstablishments"));
const ManageEstablishmentTypes = lazy(() => import("./pages/admin/configs/ManageEstablishmentTypes"));
const ManageRolePerm = lazy(() => import("./pages/admin/configs/ManageRolePerm"));
const ManageAbnTypes = lazy(() => import("./pages/admin/configs/ManageAbnTypes"));
const ManageDuplicateMotifs = lazy(() => import("./pages/admin/configs/ManageDuplicateMotifs"));
const ManageCardsFee = lazy(() => import("./pages/admin/configs/ManageCardsFee"));
const ManagePaymentMethods = lazy(() => import("./pages/admin/configs/ManagePaymentMethods"));
const ManageRoutes = lazy(() => import("./pages/admin/configs/ManageRoutes"));
const ManageLines = lazy(() => import("./pages/admin/configs/ManageLines"));
const ManageCampaigns = lazy(() => import("./pages/admin/configs/ManageCampaigns"));
const ManageSalesPeriods = lazy(() => import("./pages/admin/configs/ManageSalesPeriods"));
const ManageAgencies = lazy(() => import("./pages/admin/configs/ManageAgencies"));
const ManageSalesPoints = lazy(() => import("./pages/admin/configs/ManageSalesPoints"));
const ManageAssignAgents = lazy(() => import("./pages/admin/configs/ManageAssignAgents"));
const ManageSchoolDegrees = lazy(() => import("./pages/admin/configs/ManageSchoolDegrees"));
const ManageAcademicYears = lazy(() => import("./pages/admin/configs/ManageAcademicYears"));
const ManagePeiodicities = lazy(() => import("./pages/admin/configs/ManagePeiodicities"));
const ManageTariffBases = lazy(() => import("./pages/admin/configs/TariffBases"));

const ManageCardTypes = lazy(() => import("./pages/admin/configs/ManageCardTypes"));
const ManageClientTypes = lazy(() => import("./pages/admin/configs/ManageClientTypes"));
const ManageDiscounts = lazy(() => import("./pages/admin/configs/ManageDiscounts"));
const ManageStockCards = lazy(() => import("./pages/admin/configs/ManageStockCards"));
const ManageSeasons = lazy(() => import("./pages/admin/configs/ManageSeasons"));
const ManageLocationSeasons = lazy(() => import("./pages/admin/configs/ManageLocationSeasons"));
const ManageTypeVehiculeSaisonLocations = lazy(() => import("./pages/admin/configs/ManageTypeVehiculeSaisonLocations"));
const ManageTypeVehicules = lazy(() => import("./pages/admin/configs/ManageTypeVehicules"));
const ManageLocationTypes = lazy(() => import("./pages/admin/configs/ManageLocationTypes"));
const ManageTypeVehicleTypeLocations = lazy(() => import("./pages/admin/configs/ManageTypeVehicleTypeLocations"));
const ManageConfigs = lazy(() => import("./pages/admin/configs/ManageConfigs"));
const ManageOptions = lazy(() => import("./pages/admin/configs/ManageOptions"));
const ManageStats = lazy(() => import("./pages/admin/configs/Statistiques/ManageStats.tsx"));

/*|--------------------------------------------------------------------------
| admin pages - audits
|-------------------------------------------------------------------------- */
const ManageAudits = lazy(() => import("./pages/admin/configs/audits/ManageAudits"));
const AuditStatistics = lazy(() => import("./pages/admin/configs/audits/AuditStatistics"));


/*|--------------------------------------------------------------------------
| admin pages - subscriptions
|-------------------------------------------------------------------------- */

const ManageNewSubs = lazy(() => import("./pages/admin/subscriptions/ManageNewSubs"));



const App: React.FunctionComponent = () => {

    const { i18n } = useTranslation();
    useRefreshToken();

    const isBlocked = useSingleTabGuard();

    const dispatch = useDispatch<AppDispatch>();
    const { isAuthenticated } = useSelector((state: any) => state.auth);

    const [loading, setLoading] = useState(true);

    useEffect(() => {
        dispatch(checkAuthStatus());
    }, [dispatch]);

    useEffect(() => {
        if (isAuthenticated !== null) {
            setLoading(false);
        }
    }, [isAuthenticated]);



    if(isBlocked) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
                <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-sm border border-gray-200">
                    <h2 className="text-xl text-center font-semibold text-gray-800 mb-3">
                        {t('singleTabGuard.title')}
                    </h2>
                    
                    <div className="text-gray-600 mb-6">
                    <p className="mb-2 text-center">
                        {t('singleTabGuard.message_line1')}
                    </p>
                    <p className="text-center">
                        {t('singleTabGuard.message_line2')}
                    </p>
                    </div>
                    
                    <button 
                    onClick={() => window.location.reload()}
                    className="w-full py-2 bg-red-700 hover:bg-red-800 text-white font-medium rounded-md transition-colors"
                    >
                    {t('singleTabGuard.refresh_button')}
                    </button>
                </div>
            </div>
        );
    }   

    if (loading) {
        return <Loading />;
    }

    /*|--------------------------------------------------------------------------
    | Routes accessibles pour tous les utilisateurs
    |-------------------------------------------------------------------------- */
    const GuestRoutes = (
        <Route path="/" element={<GuestLayout />}>
            <Route index element={<Login />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/not-found" element={<NotFound />} />
            <Route path="/unauthorized" element={<AccessDenied />} />
            <Route path="*" element={<NotFound />} />
        </Route>
    );

    /*|--------------------------------------------------------------------------
    | Routes accessibles pour les utilisateurs authentifiés
    |-------------------------------------------------------------------------- */
    const AuthRoutes = (
        <Route
            path="/auth/*"
            element={
                isAuthenticated ? (
                    <AuthLayout />
                ) : (
                    <Navigate to="/unauthorized" replace />
                )
            }
        >
            {/*|--------------------------------------------------------------------------
            | PROFILES
            |-------------------------------------------------------------------------- */}
            <Route path="profile" element={
                <Suspense fallback={<Loading  />}>
                    <ManageProfile />
                </Suspense>
            }/>
            {/*|--------------------------------------------------------------------------
            | Dashboards
            |-------------------------------------------------------------------------- */}
            <Route path="admin-dashboard" element={
                <Suspense fallback={<Loading  />}>
                    <Dashboard />
                </Suspense>
            }/>
            {/*|--------------------------------------------------------------------------
            | Security section
            |-------------------------------------------------------------------------- */}
            <Route path="manage-admins" element={
                <Suspense fallback={<Loading  />}>
                    <ManageAdmins />
                </Suspense>
            }/>
            <Route path="roles-permissions" element={
                <Suspense fallback={<Loading />}>
                    <ManageRolePerm />
                </Suspense>
            }/>

            {/*|--------------------------------------------------------------------------
            | Configs section
            |-------------------------------------------------------------------------- */}


            <Route path="frais-cartes" element={
                <Suspense fallback={<Loading />}>
                    <ManageCardsFee />
                </Suspense>
            }/>
            <Route path="gouvernorats" element={
                <Suspense fallback={<Loading />}>
                    <ManageGovernorates />
                </Suspense>
            }/>
            <Route path="delegations" element={
                <Suspense fallback={<Loading />}>
                    <ManageDelegations />
                </Suspense>
            }/>
             <Route path="type-etablissement" element={
                <Suspense fallback={<Loading />}>
                    <ManageEstablishmentTypes />
                </Suspense>
            }/>
            <Route path="etablissements" element={
                <Suspense fallback={<Loading />}>
                    <ManageEstablishments />
                </Suspense>
            }/>
             <Route path="niveaux-scolaires" element={
                <Suspense fallback={<Loading />}>
                    <ManageSchoolDegrees />
                </Suspense>
            }/>
             <Route path="academic-years" element={
                <Suspense fallback={<Loading />}>
                    <ManageAcademicYears />
                </Suspense>
            }/>
             <Route path="saisons" element={
                <Suspense fallback={<Loading />}>
                    <ManageSeasons />
                </Suspense>
            }/>
            <Route path="location-seasons" element={
                <Suspense fallback={<Loading />}>
                    <ManageLocationSeasons />
                </Suspense>
            }/>
            <Route path="vehicle-season-pricing" element={
                <Suspense fallback={<Loading />}>
                    <ManageTypeVehiculeSaisonLocations />
                </Suspense>
            }/>
            <Route path="type-vehicules" element={
                <Suspense fallback={<Loading />}>
                    <ManageTypeVehicules />
                </Suspense>
            }/>
            <Route path="location-types" element={
                <Suspense fallback={<Loading />}>
                    <ManageLocationTypes />
                </Suspense>
            }/>
            <Route path="type-vehicle-type-locations" element={
                <Suspense fallback={<Loading />}>
                    <ManageTypeVehicleTypeLocations />
                </Suspense>
            }/>
            <Route path="stations" element={
                <Suspense fallback={<Loading />}>
                    <ManageStations />
                </Suspense>
            }/>
            <Route path="trajets" element={
                <Suspense fallback={<Loading />}>
                    <ManageRoutes />
                </Suspense>
            }/>
            <Route path="lignes" element={
                <Suspense fallback={<Loading />}>
                    <ManageLines />
                </Suspense>
            }/>
            <Route path="compagnes" element={
                <Suspense fallback={<Loading />}>
                    <ManageCampaigns />
                </Suspense>
            }/>
            <Route path="periode-vente" element={
                <Suspense fallback={<Loading />}>
                    <ManageSalesPeriods />
                </Suspense>
            }/>
            <Route path="peiodicitees" element={
                <Suspense fallback={<Loading />}>
                    <ManagePeiodicities />
                </Suspense>
            }/>
            <Route path="agences" element={
                <Suspense fallback={<Loading />}>
                    <ManageAgencies />
                </Suspense>
            }/>
             <Route path="points-ventes" element={
                <Suspense fallback={<Loading />}>
                    <ManageSalesPoints />
                </Suspense>
            }/>
             <Route path="affectation-agents" element={
                <Suspense fallback={<Loading />}>
                    <ManageAssignAgents />
                </Suspense>
            }/>
             <Route path="stock-cartes" element={
                <Suspense fallback={<Loading />}>
                    <ManageStockCards />
                </Suspense>
            }/>
            <Route path="types-abonnements" element={
                <Suspense fallback={<Loading />}>
                    <ManageAbnTypes />
                </Suspense>
            }/>
            <Route path="types-cartes" element={
                <Suspense fallback={<Loading />}>
                    <ManageCardTypes />
                </Suspense>
            }/>
            <Route path="methodes-paiements" element={
                <Suspense fallback={<Loading />}>
                    <ManagePaymentMethods />
                </Suspense>
            }/>
            <Route path="motifs-duplication" element={
                <Suspense fallback={<Loading />}>
                    <ManageDuplicateMotifs />
                </Suspense>
            }/>
            <Route path="configs" element={
                <Suspense fallback={<Loading />}>
                    <ManageConfigs />
                </Suspense>
            }/>
            <Route path="options" element={
                <Suspense fallback={<Loading />}>
                    <ManageOptions />
                </Suspense>
            }/>
            <Route path="base-tarifaires" element={
                <Suspense fallback={<Loading />}>
                    <ManageTariffBases />
                </Suspense>
            }/>
            <Route path="remises" element={
                <Suspense fallback={<Loading />}>
                    <ManageDiscounts />
                </Suspense>
            }/>



            {/*|--------------------------------------------------------------------------
            | Subscriptions section
            |-------------------------------------------------------------------------- */}
            <Route path="clients" element={
                <Suspense fallback={<Loading />}>
                    <ManageClients />
                </Suspense>
            }/>
            <Route path="abonnements" element={
                <Suspense fallback={<Loading />}>
                    <ManageNewSubs />
                </Suspense>
            }/>
            <Route path="statistiques" element={
                <Suspense fallback={<Loading />}>
                    <ManageStats />
                </Suspense>
            }/>

            {/*|--------------------------------------------------------------------------
            | Audits section
            |-------------------------------------------------------------------------- */}
            <Route path="audits" element={
                <Suspense fallback={<Loading />}>
                    <ManageAudits />
                </Suspense>
            }/>
            <Route path="audits/statistics" element={
                <Suspense fallback={<Loading />}>
                    <AuditStatistics />
                </Suspense>
            }/>

            <Route path="unauthorized" element={<AccessDenied  />} />
            <Route path="*" element={<NotFound />} />
        </Route>
    );


    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: '#BC0202',
                },
            }}
            locale={i18n.language === 'ar' ? arEG : i18n.language === 'fr' ? frFR : frFR  }
            direction={i18n.language === 'ar' ? 'rtl' : 'ltr'}
        >
            <Router>
                <Suspense fallback={<Loading />}>
                    <Routes>
                        {GuestRoutes}
                        {AuthRoutes}
                    </Routes>
                </Suspense>
            </Router>
        </ConfigProvider>
    );
};

export default App;
