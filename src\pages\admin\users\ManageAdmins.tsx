import { useRef, useState } from "react";
import {
  Button,
  Modal,
  Form,
  Input,
  Popconfirm,
  Breadcrumb,
  Row,
  Col,} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { RolesManager } from "../../../components";
import {
  deleteAdmin,
  getAdmins,
  storeAdmin,
  updateAdmin,
} from "../../../features/admin/adminSlice.ts";
import { useDispatch } from "react-redux";
import { toast, ToastContainer } from "react-toastify";

function ManageAdmins() {
  const { t } = useTranslation();

  const dispatch = useDispatch();
  const actionRef = useRef<any>();

  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [viewMode, setViewMode] = useState(false);
  const [editingAdmin, setEditingAdmin] = useState<any>(null);
  const [form] = Form.useForm();

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState(1);

  {
    /*|--------------------------------------------------------------------------
    | FETCH ALL ADMINS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */}
    const handleGetAdmins = (params:any,sort:any,filter:any ) =>
    {
        return dispatch(getAdmins({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

  {
    /*|--------------------------------------------------------------------------
    | COLUMNS & BREADCRUMB
    |-------------------------------------------------------------------------- */
  }
  const columns: any = [
    {
      title: `${t("manage_users.labels.lastname")}`,
      dataIndex: "lastname",
      search: false,
      sorter: true,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, data: any) => (
        <span style={{ fontWeight: 500 }}>{data.lastname}</span>
      ),
    },
    {
      title: `${t("manage_users.labels.firstname")}`,
      dataIndex: "firstname",
      search: false,
      sorter: true,
      responsive: ["xs", "sm", "md", "lg"],
    },
    {
      title: `${t("manage_users.labels.phone")}`,
      dataIndex: "phone",
      responsive: ["xs", "sm", "md", "lg"],
      sorter: true,
      render: (_: any, data: any) => data.phone,
    },
    {
      title: `${t("manage_users.labels.address")}`,
      dataIndex: "address",
      search: false,
      sorter: true,
      responsive: ["xs", "sm", "md", "lg"],
    },
    {
      title: `${t("manage_users.labels.cin")}`,
      dataIndex: "cin",
      responsive: ["xs", "sm", "md", "lg"],
      sorter: true,
      render: (_: any, data: any) => data.cin,
    },
    {
      title: `${t("manage_users.labels.email")}`,
      dataIndex: "email",
      sorter: true,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, data: any) => data.email,
    },
    {
      title: `${t("manage_users.labels.createdAt")}`,
      dataIndex: "created_at",
      valueType: "date",
      search: false,
      sorter: true,
      responsive: ["xs", "sm", "md", "lg"],
    },
    {
      title: `${t("manage_users.labels.actions")}`,
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => (
        <div className="flex gap-1">
          <Button
            className="btn-view"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
          <Button
            className="btn-edit"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Popconfirm
            title={t("manage_users.admin.confirmDelete")}
            onConfirm={() => handleDelete(record.id)}
            okText={t("manage_users.yes")}
            cancelText={t("manage_users.no")}
          >
            <Button className="btn-delete" icon={<DeleteOutlined />} danger />
          </Popconfirm>
        </div>
      ),
    },
  ];
  const breadcrumbItems = [
    {
      title: (
        <Link className="!bg-white" to="/auth/commercial-dashboard">
          {t("auth_sidebar.categories.security")}
        </Link>
      ),
    },
    {
      title: t("manage_users.admin.title"),
    },
  ];

  {
    /*|--------------------------------------------------------------------------
    | HANDLE MODAL & VIEW & ADD & EDIT
    |-------------------------------------------------------------------------- */
  }
  const handleView = (record: any) => {
    setEditingAdmin(record);
    form.setFieldsValue(record);
    setViewMode(true);
    setModalVisible(true);
  };
  const handleEdit = (record: any) => {
    setEditingAdmin(record);
    form.setFieldsValue(record);
    setViewMode(false);
    setModalVisible(true);
  };
  const handleAdd = () => {
    setEditingAdmin(null);
    form.resetFields();
    setViewMode(false);
    setModalVisible(true);
  };

  {
    /*|--------------------------------------------------------------------------
    | HANDLE STORE & UPDATE ADMIN
    |-------------------------------------------------------------------------- */
  }
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    
    const payload = editingAdmin ? { id: editingAdmin.id, ...values } : values;
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      if (editingAdmin) {
        console.log("Updating admin:", payload);
        await dispatch(updateAdmin(payload)).unwrap();
        toast.update(toastId, {
          render: t("messages.success"),
          type: "success", 
          isLoading: false,
          autoClose: 3000,
        });
        handleReset();
      } else {
        await dispatch(storeAdmin(values)).unwrap();
        toast.update(toastId, {
          render: t("messages.success"),
          type: "success",
          isLoading: false,
          autoClose: 3000,
        });
      }
      actionRef.current?.reload();
      handleReset();
    } catch (error: any) {
      const fieldErrors: any = Object.keys(error.errors || {}).map(
        (field: any) => ({
          name: field,
          errors: [error.errors[field][0]],
        })
      );
      form.setFields(fieldErrors);
      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    } finally {
      setLoading(false);
    }
  };

  const confirmSubmit = (values: any) => {
    Modal.confirm({
      title: t("manage_users.admin.confirmAction"),
      content: editingAdmin
        ? t("manage_users.admin.confirmUpdate")
        : t("manage_users.admin.confirmAdd"),
      okText: t("manage_users.admin.yes"),
      cancelText: t("manage_users.admin.no"),
      onOk: () => handleFormSubmit(values),
      centered: true,
    });
  };

  {
    /*|--------------------------------------------------------------------------
    | HANDLE DELETE ADMIN
    |-------------------------------------------------------------------------- */
  }
  const handleDelete = async (id: number) => {
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    try {
      await dispatch(deleteAdmin(id)).unwrap();
      toast.update(toastId, {
        render: t("messages.success"),
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      actionRef.current?.reload();
    } catch (error: any) {
      handleReset();
      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    }
  };

  {
    /*|--------------------------------------------------------------------------
    | RESET FUNCTION
    |-------------------------------------------------------------------------- */
  }
  const handleReset = () => {
    setLoading(false);
    form.resetFields();    setModalVisible(false);
    setEditingAdmin(null);
  };

  return (
    <>
      <Breadcrumb className="mb-5" items={breadcrumbItems} />
      <ToastContainer />
      <Row>
        <Col span={24}>
          {/*|--------------------------------------------------------------------------
            |   - PRO_TABLE
            |-------------------------------------------------------------------------- */}
          <ProTable
            headerTitle={t("manage_users.admin.title")}
            columns={columns}
            actionRef={actionRef}
            cardBordered
            rowKey="id"
            request={async (params: any, sort: any, filter: any) => {
              setLoading(true);
              const dataFilter: any = await handleGetAdmins(
                params,
                sort,
                filter
              );
              setLoading(false);
              return {
                data: dataFilter,
                success: true,
              };
            }}
            sortDirections={["ascend", "descend"]}
            pagination={{
              pageSize: pageSize,
              total: total,
              showSizeChanger: true,
              onChange: (page) => setPageNumber(page),
              onShowSizeChange: (_, pageSize) => {
                setPageSize(pageSize);
              },
            }}
            scroll={{
              x: 800,
            }}
            search={{
              labelWidth: "auto",
              className: "bg-[#FAFAFA]",
            }}
            expandable={{
              expandedRowRender: (record: any) => (
                <RolesManager adminId={record.id} />
              ),
            }}
            toolBarRender={() => [
              <Button key="button" onClick={handleAdd} className="btn-add">
                {t("manage_users.admin.add")}
              </Button>,
            ]}
          />
        </Col>
      </Row>

      {/*|--------------------------------------------------------------------------
            |   - MODAL & FORM
            |-------------------------------------------------------------------------- */}
      <Modal
        width={1000}
        title={
          viewMode
            ? t("manage_users.admin.details")
            : editingAdmin
            ? t("manage_users.admin.edit")
            : t("manage_users.admin.add")
        }
        open={modalVisible}
        onCancel={() => handleReset()}
        onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
        confirmLoading={loading}
        okText={viewMode ? null : t("manage_users.save")}
        footer={viewMode ? null : undefined}
      >
        <Form
          className="form-inputs"
          form={form}
          layout="vertical"
          onFinish={confirmSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={t("manage_users.labels.lastname")}
                name="lastname"
                rules={[
                  {
                    required: true,
                    message: t("manage_users.errors.nameRequired"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_users.admin.placeholders.lastname")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={t("manage_users.labels.firstname")}
                name="firstname"
                rules={[
                  {
                    required: true,
                    message: t("manage_users.errors.firstNameRequired"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_users.admin.placeholders.firstname")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={t("manage_users.labels.phone")}
                name="phone"
                rules={[
                  {
                    required: true,
                    message: t("manage_users.errors.phoneRequired"),
                  },
                  {
                    pattern: /^[0-9]{8}$/,
                    message: t("manage_users.errors.phoneInvalid"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_users.admin.placeholders.phone")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={t("manage_users.labels.cin")}
                name="cin"
                rules={[
                  {
                    required: true,
                    message: t("manage_users.errors.cinRequired"),
                  },
                  {
                    pattern: /^[0-9]{8}$/,
                    message: t("manage_users.errors.cinInvalid"),
                  },
                ]}
              >
                <Input
                  placeholder={t("manage_users.admin.placeholders.cin")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label={t("manage_users.labels.address")}
            name="address"
            rules={[
              {
                required: true,
                message: t("manage_users.errors.addressRequired"),
              },
            ]}
          >
            <Input
              placeholder={t("manage_users.admin.placeholders.address")}
              disabled={viewMode}
            />
          </Form.Item>
          <Form.Item
            label={t("manage_users.labels.email")}
            name="email"
            rules={[
              {
                required: true,
                type: "email",
                message: t("manage_users.errors.emailRequired"),
              },
            ]}
          >
            <Input 
              placeholder={t("manage_users.admin.placeholders.email")}
              disabled={viewMode}
            />
          </Form.Item>         
          
          <Row gutter={16} className="password">
            <Col span={12}>
              <Form.Item
                label={t("manage_users.labels.password")}
                name="password"                rules={[
                  {
                    required: !editingAdmin,
                    message: t("manage_users.errors.passwordRequired"),
                  },
                  {
                    min: 8,
                    message: t("manage_users.errors.passwordMinLength"),
                  },
                  {
                    validator: (_, value) => {
                      if (editingAdmin && !value) {
                        return Promise.resolve();
                      }
                      if (value) {
                        const hasUpperCase = /[A-Z]/.test(value);
                        const hasLowerCase = /[a-z]/.test(value);
                        const hasNumber = /[0-9]/.test(value);
                        const hasSpecialChar = /[!@#$%^&*]/.test(value);

                        if (!hasUpperCase) {
                          return Promise.reject(t("manage_users.errors.passwordUppercase"));
                        }
                        if (!hasLowerCase) {
                          return Promise.reject(t("manage_users.errors.passwordLowercase"));
                        }
                        if (!hasNumber) {
                          return Promise.reject(t("manage_users.errors.passwordNumber"));
                        }
                        if (!hasSpecialChar) {
                          return Promise.reject(t("manage_users.errors.passwordSpecial"));
                        }
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              >
                <Input.Password 
                  
                  placeholder={t("manage_users.admin.placeholders.password")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label={t("manage_users.labels.confirmPassword")}
                name="confirmPassword"
                dependencies={["password"]}
                rules={[
                  {
                    required: !editingAdmin,
                    message: t("manage_users.errors.confirmPasswordRequired"),
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue("password") === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error(t("manage_users.errors.passwordMismatch"))
                      );
                    },
                  }),
                ]}
              >
                <Input.Password 
                 
                  placeholder={t("manage_users.admin.placeholders.confirmPassword")}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
          </Row>
          
          
        </Form>
      </Modal>
    </>
  );
}

export default ManageAdmins;
