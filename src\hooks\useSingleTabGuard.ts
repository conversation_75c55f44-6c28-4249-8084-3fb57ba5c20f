import { useEffect, useState } from 'react';

const TAB_KEY = 'app_main_tab_id';

export const useSingleTabGuard = () => {
  const [isBlocked, setIsBlocked] = useState(false);
  const tabId = Date.now().toString();

  useEffect(() => {
    const existingTab = localStorage.getItem(TAB_KEY);

    if (existingTab && existingTab !== tabId) {
      setIsBlocked(true);
    } else {
      localStorage.setItem(TAB_KEY, tabId);
    }

    const handleStorage = (e: StorageEvent) => {
      if (e.key === TAB_KEY && e.newValue && e.newValue !== tabId) {
        setIsBlocked(true);
      }
    };

    const handleBeforeUnload = () => {
      if (localStorage.getItem(TAB_KEY) === tabId) {
        localStorage.removeItem(TAB_KEY);
      }
    };

    window.addEventListener('storage', handleStorage);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('storage', handleStorage);
      window.removeEventListener('beforeunload', handleBeforeUnload);

      if (localStorage.getItem(TAB_KEY) === tabId) {
        localStorage.removeItem(TAB_KEY);
      }
    };
  }, [tabId]);

  return isBlocked;
};