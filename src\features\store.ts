import { configureStore } from '@reduxjs/toolkit';
import authReducer from './auth/authSlice.ts';
import i118nReducer from './i118n/i118nSlice.ts';
import governorateReducer from './admin/governorateSlice.ts';
import purchaseOrderReducer from './admin/purchaseOrderSlice.ts';
import lineReducer from './admin/lineSlice.ts';
import adminReducer from './admin/adminSlice.ts';
import discountReducer from './admin/discountSlice.ts';
import seasonReducer from './admin/seasonSlice.ts';
import establishmentReducer from './admin/establishmentSlice.ts';
import configReducer from './admin/configSlice.ts';
import agencyReducer from './admin/agencySlice.ts';
import typeVehiculeReducer from './admin/typeVehiculeSlice.ts';
import establishmentTypeReducer from './admin/establishmentTypeSlice.ts';
import tarifBaseReducer from './admin/tarifBaseSlice.ts';
import abnTypeReducer from './admin/abnTypeSlice.ts';
import periodicityReducer from './admin/periodicitySlice.ts';
import salesPeriodReducer from './admin/salesPeriodsSlice.ts';
import clientTypeReducer from './admin/clientTypeSlice.ts';
import schoolDegreeReducer from './admin/schoolDegreeSlice.ts';
import paymentMethodReducer from './admin/paymentMethodsSlice.ts';
import cardFeesReducer from './admin/cardFeeSlice.ts';
import salePointReducer from './admin/salePointSlice.ts';
import cardTypeReducer from './admin/cardTypeSlice.ts';
import locationTypeReducer from './admin/locationTypeSlice.ts';
import stockSliceReducer from './admin/stockCardSlice.ts';
import academicYearReducer from './admin/academicYearSlice.ts';
import agentCardsAffectationReducer from './admin/agentCardsAffectationSlice.ts';
import cardSequenceTrackingReducer from './admin/cardSequenceTrackingSlice.ts';
import subsCardSliceReducer from './admin/subsCardSlice.ts';
import dashboardReducer from './admin/dashboardSlice.ts';
import statisReducer from './admin/statisSlice.ts';
import tripsReducer from './admin/tripsSlice.ts';
import auditReducer from './admin/auditSlice.ts';
import optionReducer from './admin/optionSlice.ts';
import campaignReducer from './admin/campaignSlice.ts';


const store = configureStore({
    reducer: {
        auth: authReducer,
        i118n: i118nReducer,
        academicYear: academicYearReducer,
        governorate: governorateReducer,
        purchaseOrder: purchaseOrderReducer,
        line: lineReducer,
        admin: adminReducer,
        discount: discountReducer,
        season: seasonReducer,
        establishment: establishmentReducer,
        config: configReducer,
        agency: agencyReducer,
        typeVehicule: typeVehiculeReducer,
        establishmentType: establishmentTypeReducer,
        tarifBase: tarifBaseReducer,
        abnType: abnTypeReducer,
        periodicity: periodicityReducer,
        campaign: campaignReducer,
        salesPeriod: salesPeriodReducer,
        clientType: clientTypeReducer,
        schoolDegree: schoolDegreeReducer,
        paymentMethod: paymentMethodReducer,
        cardFee: cardFeesReducer,
        salePoint: salePointReducer,
        cardType: cardTypeReducer,
        locationType: locationTypeReducer,
        stockCard:stockSliceReducer,
        agentCardsAffectation: agentCardsAffectationReducer,
        cardSequenceTracking: cardSequenceTrackingReducer,
        subsCard: subsCardSliceReducer,
        dashboard: dashboardReducer,
        statis: statisReducer,
        trips: tripsReducer,
        audit: auditReducer,
        option: optionReducer,
    },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;