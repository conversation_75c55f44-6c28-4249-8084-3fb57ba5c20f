import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col, 
    Tag, 
    Select
} from "antd";
import {EditOutlined, DeleteOutlined, EyeOutlined} from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {useDispatch} from "react-redux";
import {
    deleteLine,
    getLines,
    storeLine,
    updateLine
} from "../../../features/admin/lineSlice.ts";
import {LineStationsManager} from "../../../components";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageLines() {
    const {t,i18n} = useTranslation();
    const currentLang = i18n.language;

    const dispatch:any = useDispatch();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingLines, setEditingLines] = useState<any>(null);

    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    /*|--------------------------------------------------------------------------
    | FETCH ALL LINES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetLines = (params:any, sort:any, filter:any) =>
        dispatch(getLines({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_lines.labels.code_line")}`,
            dataIndex: "CODE_LINE",
            responsive: ["xs", "sm", "md", "lg"],
            sorter: true,
            renderFormItem: () => (
                <Input
                    allowClear
                    placeholder={t("manage_lines.filters.code_line")}
                />
            ),
        },
        {
            title: t(`manage_lines.labels.name`),
            sorter: true,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: t("manage_lines.labels.serviceType"),
            dataIndex: "type_service",
            sorter: true,
            render: (type_service: string) => (
                <Tag color={type_service.toLowerCase() === "confort" ? "pink" : "gold"}>
                    {type_service.toLowerCase() === "confort" 
                        ? t("manage_lines.labels.comfort") 
                        : t("manage_lines.labels.normal")}
                </Tag>
            ),
            renderFormItem: () => (
                <Select
                    allowClear
                    placeholder={t("manage_lines.filters.serviceType")}
                    options={[
                        { 
                            label: t("manage_lines.labels.comfort"), 
                            value: "confort" 
                        },
                        { 
                            label: t("manage_lines.labels.normal"), 
                            value: "normal" 
                        }
                    ]}
                />
            ),
        },
        {
            title: t("manage_lines.labels.status"),
            dataIndex: "status",
            sorter: true,
            responsive: ["xs", "sm", "md", "lg"],
            valueType: "select",
            render: (_: any, record: any) => (
                <Tag color={record.status === true ? 'success' : 'error'}>
                    {record.status === true ? t("common.active") : t("common.inactive")}
                </Tag>
            ),
            valueEnum: {
                "1": { 
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": { 
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        
        {
            title: `${t("manage_lines.labels.commercial_speed")}`,
            dataIndex: "commercial_speed",
            responsive: ["xs", "sm", "md", "lg"],
            sorter: true,
            renderFormItem: () => (
                <Input
                    allowClear
                    placeholder={t("manage_lines.filters.commercial_speed")}
                />
            ),
        },
        {
            title: `${t("manage_lines.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            sorter: true,
        },
        {
            title: `${t("manage_lines.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_lines") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_lines") && (
                            <Popconfirm
                                title={t("manage_lines.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("common.yes")}
                                cancelText={t("common.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.transport")}</Link>,
        },
        {
            title: t("manage_lines.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingLines(record);
        form.setFieldsValue(record);
        setViewMode(true);
        setModalVisible(true);
    };

    const handleEdit = (record: any) => {
        setEditingLines(record);
        form.setFieldsValue(record);
        setViewMode(false);
        setModalVisible(true);
    };

    const handleAdd = () => {
        setEditingLines(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE LINE
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        const payload = editingLines ? { id: editingLines.id, ...values } : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingLines) {
                console.log(payload)
                await dispatch(updateLine(payload)).unwrap();
            } else {
                console.log(payload)
                await dispatch(storeLine(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };

    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_lines.confirmAction"),
            content: editingLines
                ? t("manage_lines.confirmUpdate")
                : t("manage_lines.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE LINE
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteLine(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setViewMode(false);
        setLoading(false);
        form.resetFields();
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_lines.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter:any = await handleGetLines(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        rowKey={"id"}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize,
                            total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        expandable={{
                            expandedRowRender: (record:any) => (
                                <LineStationsManager record={record} />
                            ),
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_lines.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_lines.details")
                        : editingLines
                            ? t("manage_lines.edit")
                            : t("manage_lines.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_lines.save")}
                footer={viewMode ? null : undefined}
                confirmLoading={loading}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={loading}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={[16, 16]}>
                        <Col xs={24} sm={24}>
                            <Form.Item
                                label={t("manage_lines.labels.code_line")}
                                name="CODE_LINE"
                                rules={[{ required: true, message: t("manage_lines.errors.codeLineRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_lines.placeholders.code_line")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>

                    </Row>

                    <Row gutter={[16, 16]}>
                        <Col xs={24} sm={12}>
                        <Form.Item
                                name="status"
                                label={t("manage_salesPoints.labels.status")}
                                initialValue={true}
                                rules={[{ required: true, message: t("common.required") }]}
                            >
                                <Select 
                                    disabled={viewMode}
                                    options={[
                                        { label: t("common.active"), value: true },
                                        { label: t("common.inactive"), value: false }
                                    ]}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_lines.labels.commercial_speed")}
                                name="commercial_speed"
                                rules={[{ required: true }]}
                            >
                                <Input
                                    placeholder={t("manage_lines.placeholders.commercial_speed")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={24}>
                            <Form.Item
                                label={t("manage_lines.labels.serviceType")}
                                name="type_service"
                                rules={[{ required: true }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_lines.placeholders.serviceType")}
                                >
                                    <Select.Option value="confort">{t("manage_lines.labels.comfort")}</Select.Option>
                                    <Select.Option value="normal">{t("manage_lines.labels.normal")}</Select.Option>
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageLines;
